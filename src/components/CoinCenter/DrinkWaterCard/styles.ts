import { StyleSheet } from 'react-native';
import { px } from 'utils/px';

export const getStyles = () => {
  return StyleSheet.create({
    container: {
      backgroundColor: '#FFFFFF',
      marginHorizontal: px(16),
      marginTop: px(16),
      borderRadius: px(12)
    },
    content: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: px(16),
    },
    icon: {
      width: px(48),
      height: px(48),
      marginRight: px(12),
    },
    textContainer: {
      flex: 1,
      marginRight: px(12),
    },
    title: {
      fontSize: px(16),
      fontWeight: '600',
      color: '#333333',
      marginBottom: px(4),
    },
    subtitle: {
      fontSize: px(12),
      color: '#666666',
    },
    button: {
      backgroundColor: '#FF6B35',
      paddingHorizontal: px(20),
      paddingVertical: px(8),
      borderRadius: px(20),
    },
    buttonText: {
      fontSize: px(14),
      fontWeight: '500',
      color: '#FFFFFF',
    },
  });
};
